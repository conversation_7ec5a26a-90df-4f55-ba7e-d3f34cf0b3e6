// Application initialization file
// This file is responsible for starting background services and schedulers

import { startMonthlyEmailScheduler } from './scheduler';

let isInitialized = false;

export function initializeApp() {
  if (isInitialized) {
    return;
  }

  console.log('Initializing microfinance application...');

  try {
    // Start the monthly email scheduler
    startMonthlyEmailScheduler();
    
    isInitialized = true;
    console.log('Application initialized successfully');
  } catch (error) {
    console.error('Error initializing application:', error);
  }
}

// Auto-initialize when this module is imported
if (typeof window === 'undefined') {
  // Only run on server side
  initializeApp();
}
