import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUserId } from '../../../lib/auth';
import { 
  startMonthlyEmailScheduler, 
  stopMonthlyEmailScheduler, 
  getSchedulerStatus, 
  triggerMonthlyEmailNow,
  getNextRunTime 
} from '../../../lib/scheduler';

// GET endpoint to check scheduler status
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const userId = await getCurrentUserId(request);
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const status = getSchedulerStatus();
    const nextRun = getNextRunTime();

    return NextResponse.json({
      ...status,
      nextRun
    });

  } catch (error) {
    console.error('Error checking scheduler status:', error);
    return NextResponse.json(
      { error: 'Failed to check scheduler status' },
      { status: 500 }
    );
  }
}

// POST endpoint to control scheduler
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const userId = await getCurrentUserId(request);
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'start':
        startMonthlyEmailScheduler();
        return NextResponse.json({
          success: true,
          message: 'Monthly email scheduler started'
        });

      case 'stop':
        stopMonthlyEmailScheduler();
        return NextResponse.json({
          success: true,
          message: 'Monthly email scheduler stopped'
        });

      case 'trigger':
        await triggerMonthlyEmailNow();
        return NextResponse.json({
          success: true,
          message: 'Monthly email triggered manually'
        });

      case 'status':
        const status = getSchedulerStatus();
        const nextRun = getNextRunTime();
        return NextResponse.json({
          ...status,
          nextRun
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: start, stop, trigger, or status' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error controlling scheduler:', error);
    return NextResponse.json(
      { error: `Failed to control scheduler: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}
